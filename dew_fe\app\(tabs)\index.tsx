import { useEffect, useCallback } from "react";
import { useRouter } from "expo-router";
import * as SecureStore from 'expo-secure-store';
import { View, Text, ActivityIndicator } from "react-native";
import Colors from "../../constants/Colors";

export default function Index() {
  const router = useRouter();

  const checkAuthStatus = useCallback(async () => {
    try {
      const userId = await SecureStore.getItemAsync("user_id");
      const accessToken = await SecureStore.getItemAsync("access_token");

      if (userId && accessToken) {
        // User is logged in, redirect to dashboard
        router.replace("/(tabs)/dashboard");
      } else {
        // User is not logged in, redirect to login
        router.replace("/login");
      }
    } catch (error) {
      console.error("Error checking auth status:", error);
      // On error, redirect to login
      router.replace("/login");
    }
  }, [router]);

  useEffect(() => {
    checkAuthStatus();
  }, [checkAuthStatus]);

  return (
    <View
      className="flex-1 justify-center items-center"
      style={{ backgroundColor: Colors.background.primary }}
    >
      <ActivityIndicator size="large" color="#22c55e" />
      <Text className="text-white mt-4 text-lg">Loading...</Text>
    </View>
  );
}
